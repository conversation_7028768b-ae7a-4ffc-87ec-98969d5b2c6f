import os
os.environ["CUDA_VISIBLE_DEVICES"]="0"
os.environ["TF_CPP_MIN_LOG_LEVEL"]='3'

import tensorflow as tf
import numpy as np

def test_prediction_logic_difference():
    """
    测试两种预测逻辑的理论差异
    """
    print("=== 分析两种预测逻辑的理论差异 ===\n")
    
    print("版本1 (根目录 rawvganite_twin.py):")
    print("```python")
    print("# 在函数末尾:")
    print("test_y_hat = Inference(test_x)")
    print("return test_y_hat")
    print("```")
    print("返回: 直接的预测结果 (logits)")
    
    print("\n版本2 (分类/rawtwin/rawvganite_twin.py):")
    print("```python") 
    print("# 在函数末尾:")
    print("test_y_hat = Inference(test_x)  # 这行被计算但不使用")
    print("return Inference")
    print("```")
    print("返回: 训练好的Inference模型")
    
    print("\n=== 关键发现 ===")
    print("1. 两个版本使用完全相同的算法和网络结构")
    print("2. 版本1直接返回 Inference(test_x) 的结果")
    print("3. 版本2返回 Inference 模型，然后在主函数中调用 Inference(test_x)")
    print("4. 从数学角度看，如果模型状态相同，两种方式应该产生相同结果")
    
    print("\n=== 潜在差异来源 ===")
    print("1. 优化器参数差异:")
    print("   - 版本1: tf.keras.optimizers.Adam(lr=learning_rate)")
    print("   - 版本2: tf.keras.optimizers.Adam(learning_rate=learning_rate)")
    print("   这可能导致训练过程略有不同")
    
    print("\n2. 随机性因素:")
    print("   - 即使设置相同随机种子，TensorFlow的内部实现可能有差异")
    print("   - 不同版本的TensorFlow可能有不同的随机数生成行为")
    
    print("\n3. 内存和计算顺序:")
    print("   - 版本2在返回模型前计算了一次test_y_hat但未使用")
    print("   - 这可能影响内存状态和后续计算")
    
    print("\n=== 理论结论 ===")
    print("如果两个版本:")
    print("✅ 使用完全相同的训练数据")
    print("✅ 使用相同的随机种子")
    print("✅ 训练过程完全一致")
    print("✅ 模型参数完全相同")
    print("那么预测结果应该完全相同")
    
    print("\n但实际上可能存在微小差异，因为:")
    print("❓ 优化器参数名称差异可能影响训练")
    print("❓ TensorFlow版本兼容性问题")
    print("❓ 浮点数精度和计算顺序差异")
    
    # 创建一个简单的示例来演示
    print("\n=== 简单示例演示 ===")
    
    # 创建一个简单的模型
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(10, activation='relu'),
        tf.keras.layers.Dense(2)
    ])
    
    # 创建测试数据
    test_data = tf.random.normal((5, 5))
    
    # 方式1：直接预测并返回结果
    pred1 = model(test_data)
    
    # 方式2：返回模型，然后用模型预测
    returned_model = model
    pred2 = returned_model(test_data)
    
    # 比较结果
    diff = tf.abs(pred1 - pred2)
    max_diff = tf.reduce_max(diff)
    
    print(f"简单示例中的最大差异: {max_diff.numpy():.15f}")
    
    if max_diff < 1e-10:
        print("✅ 在简单示例中，两种方式产生完全相同的结果")
    else:
        print("❌ 在简单示例中，两种方式产生不同的结果")
    
    print("\n=== 最终答案 ===")
    print("理论上，如果模型训练过程完全相同，两种返回方式应该产生相同的预测结果。")
    print("但在实际实现中，由于以下因素可能存在微小差异：")
    print("1. 优化器参数名称差异导致的训练差异")
    print("2. TensorFlow版本兼容性")
    print("3. 浮点数精度累积误差")
    print("4. 计算图构建和执行顺序的细微差异")
    
    return True

if __name__ == "__main__":
    test_prediction_logic_difference()
