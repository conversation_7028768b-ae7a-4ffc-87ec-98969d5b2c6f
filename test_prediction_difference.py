import os
os.environ["CUDA_VISIBLE_DEVICES"]="0"
os.environ["TF_CPP_MIN_LOG_LEVEL"]='3'

import tensorflow as tf
import numpy as np
import sys

# 添加路径以便导入模块
sys.path.append('.')
sys.path.append('分类/rawtwin')

# 导入两个版本的模块
from rawvganite_twin import vganite_twin as vganite_twin_v1  # 返回预测值的版本
from datasets import data_loading_twin

# 导入分类目录版本
sys.path.insert(0, '分类/rawtwin')
from rawvganite_twin import vganite_twin as vganite_twin_v2  # 返回模型的版本

def test_prediction_consistency():
    """
    测试两种返回方式是否产生相同的预测结果
    """
    print("=== 测试两种返回方式的预测一致性 ===\n")
    
    # 设置相同的随机种子确保可重现性
    tf.random.set_seed(42)
    np.random.seed(42)
    
    # 准备数据
    print("1. 加载数据...")
    train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = data_loading_twin(0.8)
    
    # 准备参数
    parameters = {
        'data_name': 'twin',
        'batch_size': 128,
        'iteration': 100,  # 使用较少的迭代次数进行快速测试
        'learning_rate': 0.001,
        'h_dim': 15,
        'x_dim': 30
    }
    
    # 准备训练数据
    X_train = train_x
    t_train = np.reshape(train_t, (train_t.shape[0], 1))
    y_train = np.reshape(train_y, (train_y.shape[0], 1))
    
    X_test = test_x
    t_test = np.reshape(test_t, (test_t.shape[0], 1))
    y_test = np.reshape(test_y, (test_y.shape[0], 1))
    
    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    
    # 测试版本1：直接返回预测值
    print("\n2. 测试版本1 (直接返回预测值)...")
    tf.random.set_seed(42)  # 重置随机种子
    np.random.seed(42)
    
    try:
        y_hat_v1 = vganite_twin_v1(X_train, t_train, y_train, train_potential_y,
                                   X_test, test_potential_y, y_test, t_test,
                                   parameters)
        print(f"版本1预测结果形状: {y_hat_v1.shape}")
        print(f"版本1预测结果类型: {type(y_hat_v1)}")
        
        # 转换为numpy数组
        if hasattr(y_hat_v1, 'numpy'):
            y_hat_v1_np = y_hat_v1.numpy()
        else:
            y_hat_v1_np = np.array(y_hat_v1)
            
    except Exception as e:
        print(f"版本1执行出错: {e}")
        return
    
    # 测试版本2：返回模型然后预测
    print("\n3. 测试版本2 (返回模型然后预测)...")
    tf.random.set_seed(42)  # 重置随机种子
    np.random.seed(42)
    
    try:
        trained_model_v2 = vganite_twin_v2(X_train, t_train, y_train, train_potential_y,
                                          X_test, test_potential_y, y_test, t_test,
                                          parameters)
        print(f"版本2返回的对象类型: {type(trained_model_v2)}")

        # 检查返回的是模型还是张量
        if hasattr(trained_model_v2, '__call__') and not isinstance(trained_model_v2, tf.Tensor):
            # 如果是模型，使用模型进行预测
            y_hat_v2 = trained_model_v2(tf.cast(X_test, tf.float32))
            print(f"使用返回的模型进行预测")
        else:
            # 如果返回的是张量（可能是预测结果），直接使用
            print(f"返回的似乎是张量而不是模型，直接使用作为预测结果")
            y_hat_v2 = trained_model_v2

        print(f"版本2预测结果形状: {y_hat_v2.shape}")
        print(f"版本2预测结果类型: {type(y_hat_v2)}")

        # 转换为numpy数组
        if hasattr(y_hat_v2, 'numpy'):
            y_hat_v2_np = y_hat_v2.numpy()
        else:
            y_hat_v2_np = np.array(y_hat_v2)
            
    except Exception as e:
        print(f"版本2执行出错: {e}")
        return
    
    # 比较结果
    print("\n4. 比较预测结果...")
    
    # 检查形状是否相同
    if y_hat_v1_np.shape != y_hat_v2_np.shape:
        print(f"❌ 形状不同: v1={y_hat_v1_np.shape}, v2={y_hat_v2_np.shape}")
        return
    else:
        print(f"✅ 形状相同: {y_hat_v1_np.shape}")
    
    # 计算差异
    diff = np.abs(y_hat_v1_np - y_hat_v2_np)
    max_diff = np.max(diff)
    mean_diff = np.mean(diff)
    
    print(f"\n预测差异统计:")
    print(f"最大绝对差异: {max_diff:.10f}")
    print(f"平均绝对差异: {mean_diff:.10f}")
    print(f"差异标准差: {np.std(diff):.10f}")
    
    # 设置容差阈值
    tolerance = 1e-6
    
    if max_diff < tolerance:
        print(f"✅ 预测结果基本相同 (最大差异 < {tolerance})")
    else:
        print(f"❌ 预测结果存在显著差异 (最大差异 = {max_diff})")
    
    # 详细比较前几个样本
    print(f"\n前5个样本的详细比较:")
    for i in range(min(5, y_hat_v1_np.shape[0])):
        print(f"样本 {i}:")
        print(f"  版本1: [{y_hat_v1_np[i, 0]:.6f}, {y_hat_v1_np[i, 1]:.6f}]")
        print(f"  版本2: [{y_hat_v2_np[i, 0]:.6f}, {y_hat_v2_np[i, 1]:.6f}]")
        print(f"  差异:   [{diff[i, 0]:.8f}, {diff[i, 1]:.8f}]")
    
    # 统计信息比较
    print(f"\n统计信息比较:")
    print(f"版本1 - Y0: mean={np.mean(y_hat_v1_np[:, 0]):.6f}, std={np.std(y_hat_v1_np[:, 0]):.6f}")
    print(f"版本2 - Y0: mean={np.mean(y_hat_v2_np[:, 0]):.6f}, std={np.std(y_hat_v2_np[:, 0]):.6f}")
    print(f"版本1 - Y1: mean={np.mean(y_hat_v1_np[:, 1]):.6f}, std={np.std(y_hat_v1_np[:, 1]):.6f}")
    print(f"版本2 - Y1: mean={np.mean(y_hat_v2_np[:, 1]):.6f}, std={np.std(y_hat_v2_np[:, 1]):.6f}")
    
    return max_diff < tolerance

if __name__ == "__main__":
    test_prediction_consistency()
