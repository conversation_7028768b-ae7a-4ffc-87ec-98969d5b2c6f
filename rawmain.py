import os

os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["TF_CPP_MIN_LOG_LEVEL"] = '3'
import tensorflow as tf

# Standard TF 2.x GPU configuration
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        logical_gpus = tf.config.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPUs")
    except RuntimeError as e:
        print("GPU configuration error:", e)

import numpy as np
import argparse
import warnings

warnings.filterwarnings("ignore")  # To suppress warnings, e.g., from sklearn metrics

from sklearn.metrics import (accuracy_score, f1_score, roc_auc_score,
                             mean_squared_error, mean_absolute_error,
                             roc_curve, recall_score, precision_score)
import matplotlib.pyplot as plt
import matplotlib as mpl
import seaborn as sns

# --- Matplotlib 中文字体配置 ---
try:
    mpl.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'Source Han Sans CN',
                                       'sans-serif']
    mpl.rcParams['axes.unicode_minus'] = False
    print("尝试设置中文字体成功。")
except Exception as e:
    print(f"Warning: 设置中文字体失败: {e}. 图表中的中文可能无法正确显示。")

from rawvganite_twin import vganite_twin  # Using the model from your provided file
from metrics import PEHE, ATE  # Assuming you have these in metrics.py
# If data_loading_twin is not in datasets.py, adjust the import accordingly
from datasets import data_loading_twin, IHDP, Syn  # Assuming IHDP and Syn are also needed


# Helper function to find optimal threshold using Youden's J statistic (from mymain.py)
def find_optimal_threshold_youden_j_and_metrics(true_labels, pred_probas):
    """
    Finds the optimal threshold to maximize Youden's J statistic and calculates metrics.
    Youden's J = Sensitivity + Specificity - 1 = TPR - FPR.
    Includes threshold clipping to [0, 1].
    Handles cases with only one class in true_labels.
    """
    if len(np.unique(true_labels)) < 2:
        print(f"Warning: True labels for a treatment arm have only one class. Metrics calculated at 0.5 threshold.")
        optimal_threshold = 0.5
        binary_predictions = (pred_probas >= optimal_threshold).astype(int)
        accuracy = accuracy_score(true_labels, binary_predictions)
        # Ensure pos_label is set correctly, assuming 1 is the positive class
        # zero_division=0 handles cases where precision/recall are undefined (e.g., no true positives)
        precision = precision_score(true_labels, binary_predictions, pos_label=1, zero_division=0)
        recall = recall_score(true_labels, binary_predictions, pos_label=1, zero_division=0)
        f1 = f1_score(true_labels, binary_predictions, pos_label=1, zero_division=0)
        return np.nan, accuracy, precision, recall, f1  # Return NaN for threshold as it's fixed

    fpr, tpr, thresholds = roc_curve(true_labels, pred_probas, pos_label=1)

    if len(thresholds) <= 1:  # Handles cases where roc_curve returns trivial thresholds
        optimal_threshold = 0.5
    else:
        youden_j_scores = tpr - fpr
        optimal_idx = np.argmax(youden_j_scores)
        optimal_threshold = thresholds[optimal_idx]

        # Clip threshold to be within [0, 1]
        if optimal_threshold > 1.0:
            optimal_threshold = 1.0
        if optimal_threshold < 0.0:  # Probabilities should be non-negative, but as a safeguard
            optimal_threshold = 0.0

    binary_predictions = (pred_probas >= optimal_threshold).astype(int)
    accuracy = accuracy_score(true_labels, binary_predictions)
    precision = precision_score(true_labels, binary_predictions, pos_label=1, zero_division=0)
    recall = recall_score(true_labels, binary_predictions, pos_label=1, zero_division=0)
    f1 = f1_score(true_labels, binary_predictions, pos_label=1, zero_division=0)
    return optimal_threshold, accuracy, precision, recall, f1


# Plotting Function (from mymain.py)
def plot_prediction_distributions(y_hat_model, test_potential_y_true, method_name="Model"):
    """
    Plots and saves histograms of predicted probabilities and ITEs.

    Args:
        y_hat_model (np.array): Model's predicted potential outcome probabilities. Shape (n_samples, 2).
        test_potential_y_true (np.array): True potential outcomes. Shape (n_samples, 2).
        method_name (str): Name of the method for titles and filenames.
    """
    print(f"\n--- 开始为 {method_name} 生成预测分布图 ---")
    pred_probas_y0 = y_hat_model[:, 0]
    pred_probas_y1 = y_hat_model[:, 1]
    predicted_ites = pred_probas_y1 - pred_probas_y0

    true_y0 = test_potential_y_true[:, 0].astype(int)  # Ensure integer for boolean indexing
    true_y1 = test_potential_y_true[:, 1].astype(int)  # Ensure integer for boolean indexing

    # --- 打印统计摘要 ---
    print("\n预测概率和ITE的统计摘要:")
    print(
        f"  预测 P(Y(0)=1) 概率: Min={np.min(pred_probas_y0):.4f}, Mean={np.mean(pred_probas_y0):.4f}, Median={np.median(pred_probas_y0):.4f}, Max={np.max(pred_probas_y0):.4f}, StdDev={np.std(pred_probas_y0):.4f}")
    print(
        f"  预测 P(Y(1)=1) 概率: Min={np.min(pred_probas_y1):.4f}, Mean={np.mean(pred_probas_y1):.4f}, Median={np.median(pred_probas_y1):.4f}, Max={np.max(pred_probas_y1):.4f}, StdDev={np.std(pred_probas_y1):.4f}")
    print(
        f"  预测 ITE:         Min={np.min(predicted_ites):.4f}, Mean={np.mean(predicted_ites):.4f}, Median={np.median(predicted_ites):.4f}, Max={np.max(predicted_ites):.4f}, StdDev={np.std(predicted_ites):.4f}")

    # --- 绘制和保存直方图 ---
    print("\n正在生成预测分布直方图 (保存为文件)...")
    try:
        # 图1: 整体分布
        plt.figure(figsize=(18, 5))

        plt.subplot(1, 3, 1)
        sns.histplot(pred_probas_y0, kde=True, bins=30)
        plt.title(f'{method_name} - 预测 P(Y(0)=1) 的分布')
        plt.xlabel('预测概率 P(Y(0)=1)')
        plt.ylabel('频数')

        plt.subplot(1, 3, 2)
        sns.histplot(pred_probas_y1, kde=True, bins=30)
        plt.title(f'{method_name} - 预测 P(Y(1)=1) 的分布')
        plt.xlabel('预测概率 P(Y(1)=1)')
        plt.ylabel('频数')

        plt.subplot(1, 3, 3)
        sns.histplot(predicted_ites, kde=True, bins=30)
        plt.title(f'{method_name} - 预测 ITE (P(Y(1)=1)-P(Y(0)=1)) 的分布')
        plt.xlabel('预测 ITE')
        plt.ylabel('频数')

        plt.tight_layout()
        plt.savefig(f'{method_name}_predicted_distributions_overall.png')
        plt.close()
        print(f"  直方图 (整体) 已保存为: {method_name}_predicted_distributions_overall.png")

        # 图2: 按真实类别分组的分布
        plt.figure(figsize=(14, 6))  # Adjusted size for two subplots

        plt.subplot(1, 2, 1)
        sns.histplot(pred_probas_y0[true_y0 == 0], kde=True, bins=30, color='blue', label='真实 Y(0)=0', element="step",
                     alpha=0.7)
        sns.histplot(pred_probas_y0[true_y0 == 1], kde=True, bins=30, color='red', label='真实 Y(0)=1', element="step",
                     alpha=0.7)
        plt.title(f'{method_name} - 预测 P(Y(0)=1) (按真实Y(0)分组)')
        plt.xlabel('预测概率 P(Y(0)=1)')
        plt.ylabel('频数')
        plt.legend()

        plt.subplot(1, 2, 2)
        sns.histplot(pred_probas_y1[true_y1 == 0], kde=True, bins=30, color='blue', label='真实 Y(1)=0', element="step",
                     alpha=0.7)
        sns.histplot(pred_probas_y1[true_y1 == 1], kde=True, bins=30, color='red', label='真实 Y(1)=1', element="step",
                     alpha=0.7)
        plt.title(f'{method_name} - 预测 P(Y(1)=1) (按真实Y(1)分组)')
        plt.xlabel('预测概率 P(Y(1)=1)')
        plt.ylabel('频数')
        plt.legend()

        plt.tight_layout()
        plt.savefig(f'{method_name}_predicted_distributions_by_true_class.png')
        plt.close()
        print(f"  直方图 (按真实类别分组) 已保存为: {method_name}_predicted_distributions_by_true_class.png")

    except Exception as e:
        print(f"绘制图形时出错 for {method_name}: {e}. 请确保已安装 matplotlib 和 seaborn.")
    print(f"--- {method_name} 预测分布图生成完毕 ---")


def main(args):
    parameters = dict()
    parameters['data_name'] = args.data_name
    parameters['batch_size'] = args.batch_size
    parameters['iteration'] = args.iteration
    parameters['learning_rate'] = args.learning_rate
    parameters['h_dim'] = args.h_dim
    parameters['x_dim'] = args.x_dim  # Make sure this is set correctly based on data

    metric_results = dict()

    if args.data_name == "twin":
        # Assuming data_loading_twin returns: train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y
        # Adjust if the return order or content is different.
        train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y_orig = data_loading_twin(
            0.8)  # Using 0.8 as in mymain.py

        print(f"{args.data_name} dataset is ready.")
        # Ensure test_x matches the expected x_dim for the model
        # parameters['x_dim'] = train_x.shape[1] # Or set it directly if known

        X_train = train_x
        t_train = np.reshape(train_t, (train_t.shape[0], 1))  # Make robust to input shape
        y_train = np.reshape(train_y, (train_y.shape[0], 1))

        X_test = test_x
        t_test = np.reshape(test_t, (test_t.shape[0], 1))
        y_test_fact = np.reshape(test_y, (test_y.shape[0], 1))  # Factual outcomes for test set

        # Get model predictions (potential outcomes)
        # The vganite_twin function from rawvganite_twin.py returns predictions
        y_hat_tf = vganite_twin(X_train, t_train, y_train, train_potential_y,
                                X_test, test_potential_y_orig, y_test_fact, t_test,  # Pass factual y_test
                                parameters)

        # Convert TensorFlow tensors to NumPy arrays for metric calculations and plotting
        if isinstance(y_hat_tf, tf.Tensor):
            y_hat_np = y_hat_tf.numpy()
        else:
            y_hat_np = np.array(y_hat_tf)  # Ensure it's a NumPy array

        if isinstance(test_potential_y_orig, tf.Tensor):
            test_potential_y_np = test_potential_y_orig.numpy()
        else:
            test_potential_y_np = np.array(test_potential_y_orig)

        # Ensure potential outcomes are floats for PEHE/ATE and binary for classification
        test_potential_y_float = test_potential_y_np.astype(float)
        test_potential_y_binary = test_potential_y_np.astype(int)  # For classification metrics

        # --- Original Metrics from rawmain.py ---
        true_ate_val = np.mean(test_potential_y_float[:, 1] - test_potential_y_float[:, 0])
        pred_ate_val = np.mean(y_hat_np[:, 1] - y_hat_np[:, 0])
        metric_results['Original_Test_T_ATE'] = np.round(true_ate_val, 4)
        metric_results['Original_Test_hat_ATE'] = np.round(pred_ate_val, 4)
        metric_results['Original_Test_PEHE'] = np.round(PEHE(test_potential_y_float, y_hat_np), 4)
        metric_results['Original_Test_ATE_Error'] = np.round(ATE(test_potential_y_float, y_hat_np),
                                                             4)  # Error vs True PO ATE

        print("\n--- Original Metrics (from rawmain.py logic) ---")
        print(f"Test_T_ATE (True Potential Outcome ATE): {metric_results['Original_Test_T_ATE']:.4f}")
        print(f"Test_hat_ATE (Predicted Potential Outcome ATE): {metric_results['Original_Test_hat_ATE']:.4f}")
        print(f"Test_PEHE: {metric_results['Original_Test_PEHE']:.4f}")
        print(f"Test_ATE_Error (vs True PO ATE): {metric_results['Original_Test_ATE_Error']:.4f}")

        # --- Additional Classification Metrics (inspired by mymain.py) ---
        print('\n--- Calculating Additional Classification Metrics for Y(0) and Y(1) ---')
        print('(Using optimal threshold via Youden J for Acc, Prec, Rec, F1)')


        for i, treatment_label in enumerate(["Y(0)", "Y(1)"]):
            true_labels_binary = test_potential_y_binary[:, i]
            y_hat_np_sig = tf.sigmoid(y_hat_np).numpy()
            predicted_probabilities = y_hat_np_sig[:, i]

            print(f"\nMetrics for {treatment_label}:")

            # Classification metrics using optimal threshold
            opt_thresh, acc, prec, rec, f1_val = \
                find_optimal_threshold_youden_j_and_metrics(true_labels_binary, predicted_probabilities)

            metric_results[f'{treatment_label}_Optimal_Threshold_YoudenJ'] = np.round(opt_thresh, 4) if not np.isnan(
                opt_thresh) else 'N/A (single_class_true)'
            metric_results[f'{treatment_label}_Accuracy_at_OptThresh'] = np.round(acc, 4)
            metric_results[f'{treatment_label}_Precision_at_OptThresh_for_1'] = np.round(prec, 4)
            metric_results[f'{treatment_label}_Recall_at_OptThresh_for_1'] = np.round(rec, 4)
            metric_results[f'{treatment_label}_F1_Score_at_OptThresh_for_1'] = np.round(f1_val, 4)

            print(f"  Optimal Threshold (Youden J): {metric_results[f'{treatment_label}_Optimal_Threshold_YoudenJ']}")
            print(f"  Accuracy (at Opt Thresh): {metric_results[f'{treatment_label}_Accuracy_at_OptThresh']:.4f}")
            print(
                f"  Precision (for class 1 at Opt Thresh): {metric_results[f'{treatment_label}_Precision_at_OptThresh_for_1']:.4f}")
            print(
                f"  Recall (for class 1 at Opt Thresh): {metric_results[f'{treatment_label}_Recall_at_OptThresh_for_1']:.4f}")
            print(
                f"  F1-Score (for class 1 at Opt Thresh): {metric_results[f'{treatment_label}_F1_Score_at_OptThresh_for_1']:.4f}")

            # AUC Score
            try:
                if len(np.unique(true_labels_binary)) < 2:
                    raise ValueError("Only one class present in true labels for AUC.")
                auc_score = roc_auc_score(true_labels_binary, predicted_probabilities)
                metric_results[f'{treatment_label}_AUC'] = np.round(auc_score, 4)
                print(f"  AUC: {metric_results[f'{treatment_label}_AUC']:.4f}")
            except ValueError as e:
                print(f"  Could not compute AUC for {treatment_label}: {e}")
                metric_results[f'{treatment_label}_AUC'] = np.nan

        print("---------------------------------------------------------------------\n")

        # --- Call plotting function ---
        # Ensure y_hat_np and test_potential_y_np are NumPy arrays
        y_hat_prob = tf.sigmoid(y_hat_np).numpy()
        print("logits range:", y_hat_np.min(), y_hat_np.max())
        print("probas range:", y_hat_prob.min(), y_hat_prob.max())
        plot_prediction_distributions(y_hat_prob, test_potential_y_np, method_name="VGANITE_Twin_Results")
        print("test set size:", y_hat_prob.shape[0])


    # Placeholder for other datasets if you uncomment them
    elif args.data_name == "ihdp":
        print("IHDP dataset processing and metric calculation would go here.")
        # train, test, contfeats, binfeats = IHDP(path="IHDP", reps=12)
        # (x_train, t_train, y_train), true_ite_train,train_potential_y = train
        # (x_test, t_test, y_test), true_ite_test,test_potential_y = test
        # ... (rest of IHDP logic) ...
        # y_hat = vganite_ihdp(...) # Assuming a similar structure if you have vganite_ihdp
        # ... (calculate metrics for IHDP) ...
        pass  # Replace with actual IHDP logic

    elif args.data_name == "syn" or args.data_name == "syn_no":
        print(f"{args.data_name} dataset processing and metric calculation would go here.")
        # train, test = Syn(path = './data/Syn_1.0_1.0_0/8_8_4',reps=1) # Example path
        # ... (rest of Syn logic) ...
        # y_hat = vganite_syn(...) or vganite_syn_nozc(...)
        # ... (calculate metrics for Syn) ...
        pass  # Replace with actual Syn logic

    else:
        print(f"Dataset {args.data_name} is not yet implemented for these detailed metrics.")

    # You might want to return all metrics if needed outside this function
    # return metric_results


if __name__ == '__main__':
    # Inputs for the main function
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--data_name',
        choices=['twin', 'ihdp', 'syn', 'syn_no'],
        default='twin',
        type=str)
    parser.add_argument(
        '--x_dim',  # This might be dynamically set from data, or ensure it's correct for 'twin'
        help='Feature dimensions (should be optimized or set based on data)',
        default=30,  # From your original rawmain.py default for twin
        type=int)
    parser.add_argument(
        '--iteration',
        help='Training iterations (should be optimized)',
        default=500,  # From your original rawmain.py default
        type=int)
    parser.add_argument(
        '--batch_size',
        help='the number of samples in mini-batch (should be optimized)',
        default=128,  # From your original rawmain.py default
        type=int)
    parser.add_argument(
        '--learning_rate',
        choices=[0.001, 0.0001],
        default=0.001,  # From your original rawmain.py default
        type=float
    )
    parser.add_argument(
        '--h_dim',
        help='hidden state dimensions (should be optimized)',
        default=15,  # From your original rawmain.py default for VAE's h_dim for generator
        type=int)
    args = parser.parse_args()

    print("\n--- Experiment Configuration ---")
    for arg_name in vars(args):
        print(f"{arg_name}: {getattr(args, arg_name)}")
    print("--------------------------------\n")

    main(args)
    print("\nExperiment finished.")